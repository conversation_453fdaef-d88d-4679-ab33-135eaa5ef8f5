#ifndef _AD9833_H_
#define _AD9833_H_

#include "sys.h"

#define AD9833_FSYNC 	PAout(5)
#define AD9833_SCLK 	PAout(6)
#define AD9833_SDATA 	PAout(7)

#define AD9833_FSYNC1 	PBout(0)
#define AD9833_SCLK1 	PBout(1)
#define AD9833_SDATA1 	PBout(2)

/******************************************************************************/
/* AD9833                                                                    */
/******************************************************************************/
/* 寄存器 */

#define AD9833_REG_CMD		(0 << 14)
#define AD9833_REG_FREQ0	(1 << 14)
#define AD9833_REG_FREQ1	(2 << 14)
#define AD9833_REG_PHASE0	(6 << 13)
#define AD9833_REG_PHASE1	(7 << 13)

/* 命令控制位 */

#define AD9833_B28				(1 << 13)
#define AD9833_HLB				(1 << 12)
#define AD9833_FSEL0			(0 << 11)
#define AD9833_FSEL1			(1 << 11)
#define AD9833_PSEL0			(0 << 10)
#define AD9833_PSEL1			(1 << 10)
#define AD9833_PIN_SW			(1 << 9)
#define AD9833_RESET			(1 << 8)
#define AD9833_SLEEP1			(1 << 7)
#define AD9833_SLEEP12		(1 << 6)
#define AD9833_OPBITEN		(1 << 5)
#define AD9833_SIGN_PIB		(1 << 4)
#define AD9833_DIV2				(1 << 3)
#define AD9833_MODE				(1 << 1)

#define AD9833_OUT_SINUS		((0 << 5) | (0 << 1) | (0 << 3))//正弦波 
#define AD9833_OUT_TRIANGLE	((0 << 5) | (1 << 1) | (0 << 3))//三角波
#define AD9833_OUT_MSB			((1 << 5) | (0 << 1) | (1 << 3)) //方波
#define AD9833_OUT_MSB2			((1 << 5) | (0 << 1) | (0 << 3))

/******************************************************************************/
/* AD9833                                                                    */
/******************************************************************************/
/* 寄存器 */

#define AD9833_REG1_CMD		(0 << 14)
#define AD9833_REG1_FREQ0	(1 << 14)
#define AD9833_REG1_FREQ1	(2 << 14)
#define AD9833_REG1_PHASE0	(6 << 13)
#define AD9833_REG1_PHASE1	(7 << 13)

/* 命令控制位 */

#define AD9833_B281				(1 << 13)
#define AD9833_HLB1				(1 << 12)
#define AD9833_FSEL01			(0 << 11)
#define AD9833_FSEL11			(1 << 11)
#define AD9833_PSEL01			(0 << 10)
#define AD9833_PSEL11			(1 << 10)
#define AD9833_PIN_SW1			(1 << 9)
#define AD9833_RESET1			(1 << 8)
#define AD9833_SLEEP11			(1 << 7)
#define AD9833_SLEEP121		(1 << 6)
#define AD9833_OPBITEN1		(1 << 5)
#define AD9833_SIGN_PIB1		(1 << 4)
#define AD9833_DIV21			(1 << 3)
#define AD9833_MODE1				(1 << 1)

#define AD9833_OUT_SINUS1		((0 << 5) | (0 << 1) | (0 << 3))//正弦波 
#define AD9833_OUT_TRIANGLE1	((0 << 5) | (1 << 1) | (0 << 3))//三角波
#define AD9833_OUT_MSB1			((1 << 5) | (0 << 1) | (1 << 3)) //方波
#define AD9833_OUT_MSB21			((1 << 5) | (0 << 1) | (0 << 3))

void AD983_GPIO_Init(void);//初始化IO口
void AD983_GPIO_Init1(void);//初始化IO口
void AD9833_Init(void);//初始化IO口及寄存器
void AD9833_Init1(void);//初始化IO口及寄存器

void AD9833_Reset(void);			//置位AD9833的复位位
void AD9833_Reset1(void);			//置位AD9833的复位位

void AD9833_ClearReset(void);	//清除AD9833的复位位
void AD9833_ClearReset1(void);	//清除AD9833的复位位

void AD9833_SetRegisterValue(unsigned short regValue);//将值写入寄存器
void AD9833_SetRegisterValue1(unsigned short regValue);//将值写入寄存器

void AD9833_SetFrequency(unsigned short reg, float fout,unsigned short type);	//写入频率寄存器
void AD9833_SetFrequency1(unsigned short reg, float fout,unsigned short type);	//写入频率寄存器

void AD9833_SetPhase(unsigned short reg, unsigned short val);									//写入相位寄存器

void AD9833_Setup(unsigned short freq,unsigned short phase,unsigned short type);//选择频率、相位和波形类型

void AD9833_SetFrequencyQuick(float fout,unsigned short type);//设置频率及波形类型
void AD9833_SetFrequencyQuick1(float fout,unsigned short type);//设置频率及波形类型

#endif 
