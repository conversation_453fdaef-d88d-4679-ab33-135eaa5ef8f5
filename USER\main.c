#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h" 
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"  
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "AD9833.h"	
#include <stdbool.h>

bool Separate=false;

extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;

uint32_t frequency_A,frequency_B;
 
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A,phase_B,phase;

extern float frequency;
int frequency2_A;
int frequency2_B;
double frequency2_AA,frequency2_BB;

float phase_A_CS=0.0f;
float phase_B_CS=0.0f;
float phase_A_SX=0.0f;
float phase_B_SX=0.0f;

uint32_t peak_idx;
u8 QCZ=100;
u8 QCZ1=0;

int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase=0;

float ZE;
int SBP=0;

uint16_t waveform_A;
uint16_t waveform_B;


int main(void)
{
    arm_cfft_radix4_init_f32(&scfft,FFT_LENGTH,0,1);//初始化scfft结构体，设定FFT相关参数
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//设置系统中断优先级分组2
	  uart_init(115200);
	  delay_init(168);
	  LED_Init();
    Adc_Init(); //初始化adc
    Adc2_Init();
    Adc3_Init();
    DMA1_Init();//初始化dma
    DMA2_Init();//初始化dma
    DMA3_Init();//初始化dma
    AD9833_Init();//IO口及AD9833寄存器初始化
    AD9833_Init1();//IO口及AD9833寄存器初始化

    sampfre=409756;   //设置采样的频率

    TIM3_Int_Init(5-1,41-1);	 
    TIM4_Int_Init(1000-1,8400-1);//定时器时钟84M，分频系数8400，所以84M/8400=10Khz的计数频率，计数5000次为500ms  

    TIM_Cmd(TIM3, ENABLE);
     
     
 while(1)     
 {  
   /******************************得到信号A和信号B***********************************************/    
     while(flag_ADC==0||flag_ADC1==0||flag_ADC2==0) {} //等待采样完成
         
         QCZ_FFT(buff_adc); //采集C信号
     
        if(fft_outputbuf[peak1_idx]<500&&fft_outputbuf[peak1_idx]>420)
        { waveform_A=AD9833_OUT_SINUS; }
        if(fft_outputbuf[peak2_idx]<500&&fft_outputbuf[peak2_idx]>420)
        { waveform_B=AD9833_OUT_SINUS; }
        if(fft_outputbuf[peak1_idx]<400)
        { waveform_A=AD9833_OUT_TRIANGLE; }
        if(fft_outputbuf[peak2_idx]<400)
        { waveform_B=AD9833_OUT_TRIANGLE; }

         frequency_A=peak1_idx*100; //得A信号频率
         frequency_B=peak2_idx*100; //得B信号频率
 /******************************得到信号A和信号B***********************************************/ 
 
         
 /******************************鉴相***********************************************/

           AD9833_SetFrequencyQuick(frequency_A,waveform_A); 
           AD9833_SetFrequencyQuick1(frequency_B,waveform_B);
         
           delay_ms(100);
         
         flag_ADC=0;flag_ADC1=0;flag_ADC2=0;
         TIM_Cmd(TIM3, ENABLE);
         
         while(flag_ADC==0||flag_ADC1==0||flag_ADC2==0) {} //等待采样完成
         QCZ_FFT(buff_adc); //采集C信号
         phase_A_CS=phase_A; //提取C信号中A的相位信息
         phase_B_CS=phase_B;
         if(peak1_idx>peak2_idx) //索引误差纠正
         {
             peak_idx=peak1_idx;
             peak1_idx=peak2_idx;
             peak2_idx=peak_idx;
             phase_A_CS=phase_B;
             phase_B_CS=phase_A;
         }
         frequency_A=peak1_idx*100; //得A信号频率
         frequency_B=peak2_idx*100; //得B信号频率

         QCZ_FFT1(buff_adc2); //采集A'信号
         phase_A_SX=phase; //A'相位信息
         frequency2_A=(frequency+100)/1000; //频率取整
         frequency2_A=frequency2_A*1000;
         
        phase_difference_A=phase_A_CS-phase_A_SX; //得A与A'的相位差（鉴相）
        if(phase_difference_A>180) {phase_difference_A=phase_difference_A-180;} 
	      if(phase_difference_A<-180) { phase_difference_A=phase_difference_A+180;}
        if(phase_difference_A<0) { phase_difference_A=-phase_difference_A; }
        if(phase_difference_A>100) { phase_difference_A=180-phase_difference_A; }
          
         QCZ_FFT1(buff_adc3); //采集B'信号
         phase_B_SX=phase; //B'相位信息
         frequency2_B=(frequency+100)/1000; //频率取整
         frequency2_B=frequency2_B*1000;
         printf("%d\r\n",frequency2_B);
         phase_difference_B=phase_B_CS-phase_B_SX; //得B与B'的相位差（鉴相）
         if(phase_difference_B>180) {phase_difference_B=phase_difference_B-180;} 
         if(phase_difference_B<-180) { phase_difference_B=phase_difference_B+180;}
         if(phase_difference_B<0) { phase_difference_B=-phase_difference_B; }
         if(phase_difference_B>100) { phase_difference_B=180-phase_difference_B; }

           if(frequency2_A==frequency_A&&frequency2_B==frequency_B) { Separate=true; frequency2_AA=frequency_A; frequency2_BB=frequency_B; Phase=-1; ZE=frequency_B/frequency_A; }
           else { AD9833_SetFrequencyQuick(frequency_A,waveform_A); AD9833_SetFrequencyQuick1(frequency_B,waveform_B); } 
/******************************鉴相***********************************************/
           
           while(Separate) //开始锁相
           {
                 
                 if(Phase==-1) { QCZ_Phase[0]=phase_difference_A; QCZ_Phase1[0]=phase_difference_B; }
                 else { QCZ_Phase[0]=phase_difference_A1; QCZ_Phase1[0]=phase_difference_B1; Phase=0;  }
                 Phase++;
                 flag_ADC=0;flag_ADC1=0;flag_ADC2=0;
                 TIM_Cmd(TIM3, ENABLE);
                 while(flag_ADC==0||flag_ADC1==0||flag_ADC1==0) {} //等待采样完成

                 QCZ_FFT(buff_adc); //采集C信号
                 phase_A_CS=phase_A; //提取C信号中A的相位信息
                 phase_B_CS=phase_B;
                 if(peak1_idx>peak2_idx) //索引误差纠正
                {
                 peak_idx=peak1_idx;
                 peak1_idx=peak2_idx;
                 peak2_idx=peak_idx;
                 phase_A_CS=phase_B;
                 phase_B_CS=phase_A;
                }
                 frequency_A=peak1_idx*100; //得A信号频率
                 frequency_B=peak2_idx*100; //得B信号频率

                QCZ_FFT1(buff_adc2); //采集A'信号
                phase_A_SX=phase; //A'相位信息
                frequency2_A=(frequency+100)/1000; //频率取整
                frequency2_A=frequency2_A*1000;

                phase_difference_A1=phase_A_CS-phase_A_SX; //得A与A'的相位差（鉴相）
                if(phase_difference_A1>180) {phase_difference_A1=phase_difference_A1-180;} 
	              if(phase_difference_A1<-180) { phase_difference_A1=phase_difference_A1+180;}
                if(phase_difference_A1<0) { phase_difference_A1=-phase_difference_A1; }
                if(phase_difference_A1>100) { phase_difference_A1=180-phase_difference_A1; }
         
                QCZ_FFT1(buff_adc3); //采集B'信号
                phase_B_SX=phase; //B'相位信息
                frequency2_B=(frequency+100)/1000; //频率取整
                frequency2_B=frequency2_B*1000;
                
                phase_difference_B1=phase_B_CS-phase_B_SX; //得B与B'的相位差（鉴相）
                if(phase_difference_B1>180) {phase_difference_B1=phase_difference_B1-180;} 
                if(phase_difference_B1<-180) { phase_difference_B1=phase_difference_B1+180;}
                if(phase_difference_B1<0) { phase_difference_B1=-phase_difference_B1; }
                if(phase_difference_B1>100) { phase_difference_B1=180-phase_difference_B1; }

                QCZ_Phase[1]=phase_difference_A1;Phase=0;
                QCZ_Phase1[1]=phase_difference_B1;Phase=0;
         
         
                if(QCZ_Phase[0]>QCZ_Phase[1]) { frequency2_AA=frequency2_AA+0.01f;  AD9833_SetFrequencyQuick(frequency2_AA,waveform_A);  }
                else if(QCZ_Phase[0]<QCZ_Phase[1]) { frequency2_AA=frequency2_AA-0.0143f;  AD9833_SetFrequencyQuick(frequency2_AA,waveform_A);  }
                
                if(QCZ_Phase1[0]>QCZ_Phase1[1]) { frequency2_BB=frequency2_BB+0.02f;  AD9833_SetFrequencyQuick1(frequency2_BB,waveform_B); }
                else if(QCZ_Phase1[0]<QCZ_Phase1[1]) { frequency2_BB=frequency2_BB-0.034f;  AD9833_SetFrequencyQuick1(frequency2_BB,waveform_B); }

                printf("%f\r\n",frequency2_AA);  

                if(frequency2_A!=frequency_A||frequency2_B!=frequency_B) { Separate=false;  }
                flag_ADC=0;flag_ADC1=0;flag_ADC2=0;
                TIM_Cmd(TIM3, ENABLE);
           }
           //delay_ms(1000);
           flag_ADC=0;flag_ADC1=0;flag_ADC2=0;
           TIM_Cmd(TIM3, ENABLE);
                
           
           
           
  }

 }
