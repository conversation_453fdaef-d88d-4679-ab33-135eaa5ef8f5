#include "fft.h"
#include "kalman.h"
#include "led.h"

arm_cfft_radix4_instance_f32 scfft;
float fft_inputbuf[FFT_LENGTH*2];
float fft_outputbuf[FFT_LENGTH];
float sampfre;
u8 flag3;
float Adresult = 0,thd=0;
float k;
float frequency = 0;
u8  u,  Vpp_buff[20] = {0},fre[20]= {0},ele[20]= {0};
float elec;
int set_right;
float set_rightk=1;
allvpp_fre all_vpp_fre;
u8 len;
float effective_value[6];
float n[2043];
int m[2043];

uint32_t peak1_idx;
uint32_t peak2_idx;
uint32_t length=FFT_LENGTH;

// 输入: fft_outputbuf（幅度谱数组）, length（数组长度，如4096）
// 输出: peak1_idx（第一个峰值的索引）, peak2_idx（第二个峰值的索引）
void find_peak_indices(float* magnitude, uint32_t length, uint32_t* peak1_idx, uint32_t* peak2_idx) {
    *peak1_idx = 0;
    *peak2_idx = 0;
    float peak1_val = 0;
    float peak2_val = 0;

    // 只检查前一半频点（实数信号对称性）
    for (uint32_t i = 1; i < length/2 - 1; i++) {
        // 检测局部峰值（避免噪声和直流分量）
        if (magnitude[i] > magnitude[i-1] && magnitude[i] > magnitude[i+1]) {
            if (magnitude[i] > peak1_val) {
                // 更新峰值2为旧的峰值1
                peak2_val = peak1_val;
                *peak2_idx = *peak1_idx;
                // 更新峰值1
                peak1_val = magnitude[i];
                *peak1_idx = i;
            } else if (magnitude[i] > peak2_val) {
                // 更新峰值2
                peak2_val = magnitude[i];
                *peak2_idx = i;
            }
        }
    }
}

void FFT(__IO uint16_t* buff)//值采满后进行fft
{
    static u16 i =0;
    for(i=0; i<FFT_LENGTH; i++) //生成信号序列
    {
        fft_inputbuf[2*i]=(float32_t) buff[i]*(3.3/4096);
        fft_inputbuf[2*i+1]=0;
    }
    arm_cfft_radix4_f32(&scfft,fft_inputbuf);	//FFT计算（基4）
    arm_cmplx_mag_f32(fft_inputbuf,fft_outputbuf,FFT_LENGTH);	//把运算结果复数求模得幅值
    find_peak_indices(fft_outputbuf,length,&peak1_idx,&peak2_idx);
}


float Hanningwindow(int t)//汉宁窗
{
    float wt;
    wt=(1-cos(2*PI*t/FFT_LENGTH))/2;
    return wt;
}



float get_pianyik(u16 time) //求基于汉宁窗的双峰谱线插值法校正系数k
{
    float k;
    if(fft_outputbuf[time+1]>=fft_outputbuf[time-1])
    {
        k=(fft_outputbuf[time+1]*2-fft_outputbuf[time])/(fft_outputbuf[time+1]+fft_outputbuf[time]);
    }
    else
    {
        k=(fft_outputbuf[time]-fft_outputbuf[time-1]*2)/(fft_outputbuf[time]+fft_outputbuf[time-1]);
    }
    return k;
}



modulus_point Get_basevpp_point(float32_t* fft_outputbuf)//通过比较大小得到基波的模值与点号
{
    modulus_point first;
    u16 i;
    float max=0;
    for(i=5; i<(FFT_LENGTH/2); i++)
    {
        if(fft_outputbuf[i]>max)
        {
            max=fft_outputbuf[i];
            first.time=i;
        }
    }
    first.fftvpp_modulus=max;
    return first;
}


u16 timef;
void get_basefrevpp(void)//得到基波的幅度与频率
{

    timef=Get_basevpp_point(fft_outputbuf).time;//得到基波所在点数
    k=get_pianyik(timef);
    Adresult=(PI*k*fft_outputbuf[timef]*2*(1-k*k)/sin(PI*k))*4/FFT_LENGTH;//得到vpp,vpp校正,基于汉宁窗
//    elec=Adresult/5;
    frequency =((timef+k)*sampfre/FFT_LENGTH);//得到频率  频率校正 基于汉宁窗p
 
}



modulus_point Get_othervpp_point(modulus_point* first,float32_t* fft_outputbuf,u16 n)//得到其他各次谐波的模值与点号
{
    modulus_point other;
    other.time=(first->time)*n;
    other.fftvpp_modulus=fft_outputbuf[(first->time)*n];
    return other;
}


vpp_fre Get_vpp_fre(modulus_point* other) //通过模值与点号得到基波与各次谐波的幅度与频率
{

    vpp_fre anyvppfre;
    float k=get_pianyik(other->time);
    if(other->time>0)
    {
        anyvppfre.vpp=(PI*k*fft_outputbuf[other->time]*2*(1-k*k)/sin(PI*k))*4/FFT_LENGTH*set_rightk*2.2;
//		anyvppfre.fre=((float)(time)+k)*sampfre/FFT_LENGTH;
    }
    else
    {
        anyvppfre.vpp=(other->fftvpp_modulus)/FFT_LENGTH;
        anyvppfre.fre=(other->time)*sampfre/FFT_LENGTH;
    }
    return anyvppfre;
}


allvpp_fre n_get_vppfre(float32_t* fft_outputbuf) //求出基波于前十次谐波的值
{
    u8 i;
    modulus_point first;
    modulus_point other;
    vpp_fre one_vpp_fre;
    allvpp_fre all_vpp_fre;
    float fre_base;

    first=Get_basevpp_point(fft_outputbuf);
    fre_base=(first.time+k)*sampfre/FFT_LENGTH;
    for(i=1; i<10; i++)
    {
        other=Get_othervpp_point(&first,fft_outputbuf,i);
        one_vpp_fre=Get_vpp_fre(&other);
        all_vpp_fre.fre[i]=fre_base*i;
        all_vpp_fre.vpp[i]=one_vpp_fre.vpp;
        //all_vpp_fre.vpp[i]=kalman(i,all_vpp_fre.vpp[i]);
    }
    return all_vpp_fre;
}



void get_thd(void) //求THD值总谐波失真
{
    u8 i=0;
     
    for(i=1; i<6; i++)
    {
        effective_value[i]=(double)(all_vpp_fre.vpp[i])/2;
    }
//	thd=sqrt((effective_value[2]/effective_value[1])*(effective_value[2]/effective_value[1])+
//	(effective_value[3]/effective_value[1])*(effective_value[2]/effective_value[1]));
    thd=sqrt(effective_value[2]*effective_value[2]+
             effective_value[3]*effective_value[3]+
             effective_value[4]*effective_value[4]+
             effective_value[5]*effective_value[5])/effective_value[1]*100-0.2;
    thd=kalman_thd(thd);
}



